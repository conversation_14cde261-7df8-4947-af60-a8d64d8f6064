# Application
APP_NAME=Kuroibara
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost:8000
SECRET_KEY=your_secret_key_here
ALLOWED_HOSTS=localhost,127.0.0.1

# Database
DB_CONNECTION=postgresql
DB_HOST=postgres
DB_PORT=5432
DB_DATABASE=kuroibara
DB_USERNAME=kuroibara
DB_PASSWORD=password

# Valkey (Redis)
VALKEY_HOST=valkey
VALKEY_PORT=6379
VALKEY_PASSWORD=null
VALKEY_DB=0

# JWT
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Storage
STORAGE_TYPE=local
STORAGE_PATH=/app/storage
MAX_UPLOAD_SIZE=100MB

# Email
MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# 2FA
TWO_FA_ISSUER=Kuroibara

# Providers
# Optional: FlareSolverr URL for bypassing Cloudflare protection
# If not set, Cloudflare-protected providers will be disabled
# Example: FLARESOLVERR_URL=http://************:8191
FLARESOLVERR_URL=

# External Integrations (Optional)
# You can either set these environment variables OR configure them through the UI
# Anilist OAuth2 credentials (get from https://anilist.co/settings/developer)
# ANILIST_CLIENT_ID=
# ANILIST_CLIENT_SECRET=

# MyAnimeList OAuth2 credentials (get from https://myanimelist.net/apiconfig)
# MAL_CLIENT_ID=
# MAL_CLIENT_SECRET=
