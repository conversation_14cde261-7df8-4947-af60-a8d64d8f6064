# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.pytest_cache/
cover/

# Virtual Environment
venv/
ENV/
env/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
.pnpm-debug.log

# Coverage reports
coverage/
*.coverage
.nyc_output/
.coverage.*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Docker
.docker/

# Logs
logs/
*.log

# Media files
media/
uploads/
downloads/

# Storage and Backup directories
storage/
backups/
backend/storage/
backend/backups/

# Database
*.db

# Compiled files
*.com
*.class
*.dll
*.exe
*.o
*.so

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Personal/Private Documentation
NOTES.md
TODO.md
PRIVATE.md
INTERNAL.md
PERSONAL_*.md

# Draft/Work-in-Progress Documentation
DRAFT_*.md
WIP_*.md
TEMP_*.md
*_DRAFT.md
*_WIP.md
*_TEMP.md

# Auto-generated Documentation (uncomment if you use these)
# API_DOCS.md
# COVERAGE.md

# Provider Testing and Analysis Reports
*provider_test_results*
*provider_*_report.txt
*provider_*_report.json
*flaresolverr_test_results*
PROVIDER_HEALTH_REPORT.md

# Test Files
test_provider_fixes.py
**/test_provider_*.py
**/test_*_fixes.py

# Temporary - to fix GitHub README display issue
# CI-CD-SETUP.md
